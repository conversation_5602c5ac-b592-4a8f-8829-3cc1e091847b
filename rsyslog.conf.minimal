# rsyslog configuration file - MINIMAL VERSION FOR RHEL 8
# This version removes ALL potentially problematic parameters
# For more information see /usr/share/doc/rsyslog-*/rsyslog_conf.html

#### MODULES ####
# RHEL 8 COMPATIBLE: Only load modules if not already loaded in main config
# Most modules are already loaded in /etc/rsyslog.conf by default
# Commenting out to avoid "module already loaded" errors

# $ModLoad imuxsock # Usually already loaded in main config
# $ModLoad imjournal # Usually already loaded in main config
# $ModLoad imklog # reads kernel messages (the same are read from journald)
# $ModLoad immark  # provides --MARK-- message capability

# Provides UDP syslog reception
# $ModLoad imudp
# $UDPServerRun 514

# Provides TCP syslog reception - REMOVED to avoid port conflicts
# $ModLoad imtcp
# $InputTCPServerRun 514

#### GLOBAL DIRECTIVES ####
# Where to place auxiliary files
$WorkDirectory /var/lib/rsyslog

# Use default timestamp format
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# File syncing capability is disabled by default. This feature is usually not required,
# not useful and an extreme performance hit
#$ActionFileEnableSync on

# RHEL 8 COMPATIBLE: Avoid potential include conflicts
# Include all config files in /etc/rsyslog.d/ - COMMENTED OUT to avoid conflicts
# $IncludeConfig /etc/rsyslog.d/*.conf

# Turn off message reception via local log socket - COMMENTED OUT
# May conflict with main config settings
# $OmitLocalLogging on

# File to store the position in the journal - COMMENTED OUT
# May have permission issues or conflicts with main config
# $IMJournalStateFile imjournal.state

# QRadar Settings - moved to global section
$PreserveFQDN on

#### RULES ####
# Log all kernel messages to the console.
# Logging much else clutters up the screen.
#kern.*                                                 /dev/console

# Log anything (except mail) of level info or higher.
# Don't log private authentication messages!
*.info;mail.none;authpriv.none;cron.none                /var/log/messages

# The authpriv file has restricted access.
auth,authpriv.*                                         /var/log/secure

# Log all the mail messages in one place.
mail.*                                                  -/var/log/maillog

# Log cron stuff
cron.*                                                  /var/log/cron

# Everybody gets emergency messages
*.emerg                                                 :omusrmsg:*

# Save news errors of level crit and higher in a special file.
uucp,news.crit                                          /var/log/spooler

# Save boot messages also to boot.log
local7.*                                                /var/log/boot.log

# QRadar forwarding - using UDP port 514
local6.* @9.214.44.206:514

# Vector collector forwarding - ABSOLUTE MINIMAL configuration
# No queue parameters - let rsyslog use all defaults
*.* @@9.214.57.164:6514

# Message filtering rules (keep these at the end)
:msg, contains, "BESClient" stop
:msg, contains, "cinfo" stop
:msg, contains, "date" stop
:msg, contains, "java" stop
:msg, contains, "monitor_home_fo" stop
:msg, contains, "grep" stop
:msg, contains, "md5sum" stop
:msg, contains, "watchlist4.db2" stop
:msg, contains, "sudo_nrpe_sync" stop
:msg, contains, "awk" stop
:msg, contains, "PROCTITLE" stop
