#####################################################################
# IBM Z (s390x) VECTOR LOG FORWARDER CONFIGURATION - RHEL 8
# File: /etc/rsyslog.d/50-ibmz-vector-forwarder.conf
#
# Purpose: Forward RHEL 8 system logs to centralized Vector collector
# Target: IBM Z (s390x) mainframe systems running RHEL 8
# Vector Collector: TCP socket on port 6514
# Rsyslog Version: 8.2102.0+ (RHEL 8 default)
#
# IBM Z SPECIFIC OPTIMIZATIONS:
# 1. Memory-efficient queue management for mainframe shared memory
# 2. HiperSockets network optimization for intra-mainframe communication
# 3. CPU-efficient processing optimized for IBM Z processor characteristics
# 4. Enhanced reliability for mission-critical mainframe workloads
# 5. Optimized for z/VM and LPAR environments
#
# Author: System Administrator
# Version: 1.0 (IBM Z s390x Optimized)
# Date: 2024-12-24
#####################################################################

#####################################################################
# IBM Z CONFIGURATION VARIABLES
#####################################################################

# IMPORTANT: Replace with your actual Vector collector IP address
# For HiperSockets: Use HiperSockets IP if collector is on same mainframe
# For OSA: Use traditional IP networking
# Example HiperSockets IP range: 192.168.255.x
# Example OSA IP: Your external network IP

#####################################################################
# IBM Z MODULE LOADING (RHEL 8 Compatible)
#####################################################################

# NOTE: Most modules are already loaded in /etc/rsyslog.conf
# Avoid duplicate loading to prevent errors on IBM Z systems
# Only load modules if they are not already loaded

# Check if omfwd module is available for IBM Z
# If missing on s390x, install: yum install rsyslog-relp rsyslog-gnutls
# Alternative: Use built-in TCP forwarding without explicit module loading

#####################################################################
# IBM Z GLOBAL CONFIGURATION (Mainframe Optimized)
#####################################################################

# Working directory for queue files and state - IBM Z optimized path
$WorkDirectory /var/spool/rsyslog

# Message size limits - Conservative for IBM Z network efficiency
# Smaller messages reduce HiperSockets overhead
$MaxMessageSize 16k

# Timestamp format - IBM Z compatible
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# Preserve FQDN and structured data - Important for mainframe identification
$PreserveFQDN on
$EscapeControlCharactersOnReceive off

# IBM Z Memory Management - Conservative for shared memory environments
$MainMsgQueueSize 4096
$MainMsgQueueHighWaterMark 3072
$MainMsgQueueLowWaterMark 1024
$MainMsgQueueDiscardMark 3840

#####################################################################
# IBM Z MESSAGE TEMPLATES (HiperSockets Optimized)
#####################################################################

# IBM Z Vector Template - Optimized for mainframe metadata
$template IBMZVectorFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [ibmz@32473 arch=\"s390x\" facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" source_host=\"%HOSTNAME%\" lpar=\"%HOSTNAME%\"] %MSG%\n"

# IBM Z Compact Vector Template - Memory efficient
$template IBMZVectorCompact,"<%PRI%>%TIMESTAMP:::date-unixtimestamp% %HOSTNAME% %syslogtag:1:24%%msg:1:200%\n"

# IBM Z Standard Template - Fallback compatibility
$template IBMZStandardFormat,"<%PRI%>%TIMESTAMP% %HOSTNAME% %syslogtag%%msg%\n"

#####################################################################
# IBM Z PRIORITY-BASED FORWARDING (Memory Optimized)
#####################################################################

# IBM Z CRITICAL PRIORITY: Configure high-priority queue
# Reduced sizes for mainframe memory efficiency
$ActionQueueFileName ibmz_critical_vector
$ActionQueueMaxDiskSpace 256m
$ActionQueueSaveOnShutdown on
$ActionQueueType LinkedList
$ActionResumeRetryCount -1
$ActionQueueSize 2048
$ActionQueueDiscardMark 1920
$ActionQueueHighWaterMark 1536
$ActionQueueLowWaterMark 512
$ActionQueueWorkerThreads 1
$ActionQueueTimeoutEnqueue 100

# Forward critical messages to Vector collector
# IMPORTANT: Replace *************** with your Vector collector IP
# Use HiperSockets IP if collector is on same mainframe
*.emerg @@***************:6514;IBMZVectorFormat
*.alert @@***************:6514;IBMZVectorFormat
*.crit @@***************:6514;IBMZVectorFormat
authpriv.* @@***************:6514;IBMZVectorFormat

# Reset queue for medium priority - IBM Z optimized
$ActionQueueFileName ibmz_medium_vector
$ActionQueueMaxDiskSpace 128m
$ActionQueueSize 1024
$ActionQueueDiscardMark 960
$ActionQueueHighWaterMark 768
$ActionQueueLowWaterMark 256
$ActionQueueWorkerThreads 1

# Forward medium priority messages
kern.* @@***************:6514;IBMZVectorFormat
daemon.* @@***************:6514;IBMZVectorFormat
*.err @@***************:6514;IBMZVectorFormat
*.warning @@***************:6514;IBMZVectorFormat

# Reset queue for standard priority - IBM Z memory conscious
$ActionQueueFileName ibmz_standard_vector
$ActionQueueMaxDiskSpace 64m
$ActionQueueSize 512
$ActionQueueDiscardMark 480
$ActionQueueHighWaterMark 384
$ActionQueueLowWaterMark 128
$ActionQueueWorkerThreads 1

# Forward all other messages with compact format for efficiency
*.info @@***************:6514;IBMZVectorCompact


#####################################################################
# IBM Z ERROR HANDLING AND MONITORING
#####################################################################

# Log rsyslog errors - IBM Z enhanced monitoring
$ErrorMessagesToStderr off
$LogRSyslogStatusMessages on

# Monitor forwarding errors - Extended interval for mainframe stability
$ActionExecOnlyOnceEveryInterval 600
& /var/log/ibmz-vector-errors.log

#####################################################################
# IBM Z NETWORK OPTIMIZATION NOTES
#####################################################################

# HiperSockets Configuration:
# - Use HiperSockets for intra-mainframe communication
# - Provides memory-speed connectivity between LPARs
# - Reduces CPU overhead compared to OSA networking
# - Ideal when Vector collector is on same mainframe
#
# OSA Configuration:
# - Use for external Vector collectors
# - Configure appropriate MTU sizes
# - Consider network latency in queue timeouts
#
# Network Interface Selection:
# - HiperSockets: Typically hsi0, hsi1, etc.
# - OSA: Typically enc1, enc2, etc.
# - Check with: ip addr show

#####################################################################
# IBM Z MEMORY OPTIMIZATION NOTES
#####################################################################

# Queue Size Rationale:
# - Smaller queues reduce memory footprint
# - Optimized for z/VM shared memory model
# - Suitable for LPAR memory constraints
# - Balances performance with resource efficiency
#
# Memory Monitoring:
# - Monitor queue file sizes: du -sh /var/spool/rsyslog/ibmz_*
# - Check system memory: free -h
# - Monitor LPAR memory usage: cat /proc/meminfo

#####################################################################
# IBM Z DEPLOYMENT INSTRUCTIONS
#####################################################################

# 1. INSTALL REQUIRED PACKAGES ON IBM Z:
#    sudo yum install -y rsyslog
#    sudo yum install -y nc telnet  # For testing connectivity
#    # Optional for enhanced features:
#    sudo yum install -y rsyslog-relp rsyslog-gnutls
#
# 2. DEPLOY IBM Z VECTOR FORWARDER CONFIGURATION:
#    sudo cp ibmz-vector-forwarder-s390x.conf /etc/rsyslog.d/50-ibmz-vector-forwarder.conf
#    sudo chown root:root /etc/rsyslog.d/50-ibmz-vector-forwarder.conf
#    sudo chmod 644 /etc/rsyslog.d/50-ibmz-vector-forwarder.conf
#
# 3. UPDATE VECTOR COLLECTOR IP FOR IBM Z:
#    # For HiperSockets (intra-mainframe):
#    sudo sed -i 's/192\.168\.255\.100/YOUR_HIPERSOCKETS_IP/g' /etc/rsyslog.d/50-ibmz-vector-forwarder.conf
#    # For OSA (external network):
#    sudo sed -i 's/192\.168\.255\.100/YOUR_EXTERNAL_VECTOR_IP/g' /etc/rsyslog.d/50-ibmz-vector-forwarder.conf
#
# 4. VALIDATE CONFIGURATION ON IBM Z:
#    sudo rsyslogd -N1 -f /etc/rsyslog.conf
#
# 5. TEST IBM Z NETWORK CONNECTIVITY:
#    # Check HiperSockets interfaces (if applicable):
#    ip addr show | grep -i hsi
#    # Test Vector collector connectivity:
#    nc -zv YOUR_VECTOR_IP 6514
#    # Check IBM Z network statistics:
#    lsqeth  # For HiperSockets and OSA
#
# 6. RESTART RSYSLOG SERVICE ON IBM Z:
#    sudo systemctl restart rsyslog
#    sudo systemctl status rsyslog
#
# 7. TEST IBM Z LOGGING:
#    logger "IBM Z Vector forwarder test from $(hostname) - Architecture: $(uname -m)"
#    logger -p authpriv.warning "IBM Z s390x authentication test"
#    logger -p local0.info "IBM Z system-specific test message"
#
# 8. MONITOR IBM Z VECTOR FORWARDING:
#    sudo tail -f /var/log/ibmz-vector-errors.log
#    sudo ls -la /var/spool/rsyslog/ibmz_*_vector*
#    # Check IBM Z system resources:
#    cat /proc/sysinfo | grep -E "LPAR|VM|Processor"

#####################################################################
# IBM Z TROUBLESHOOTING GUIDE
#####################################################################

# IBM Z Architecture Verification:
# 1. Confirm s390x architecture: uname -m
# 2. Check IBM Z system info: cat /proc/sysinfo
# 3. Verify LPAR/z/VM environment: cat /proc/cpuinfo
#
# HiperSockets Troubleshooting:
# 1. List HiperSockets devices: lsqeth | grep -i hiper
# 2. Check HiperSockets status: cat /sys/bus/ccwgroup/drivers/qeth/*/online
# 3. Monitor HiperSockets performance: cat /proc/qeth_perf_stats
# 4. Test HiperSockets connectivity: ping -I hsi0 TARGET_IP
#
# OSA Network Troubleshooting:
# 1. List OSA devices: lsqeth | grep -i osa
# 2. Check OSA status: ip link show | grep enc
# 3. Monitor OSA statistics: ethtool -S enc1
#
# Memory Issues on IBM Z:
# 1. Check LPAR memory allocation: cat /proc/meminfo
# 2. Monitor rsyslog memory usage: ps aux | grep rsyslog
# 3. Check queue file sizes: du -sh /var/spool/rsyslog/
# 4. Adjust queue sizes if memory constrained
#
# CPU Performance on IBM Z:
# 1. Monitor CPU usage: vmstat 1 5
# 2. Check rsyslog CPU consumption: top -p $(pgrep rsyslogd)
# 3. Verify IBM Z processor info: cat /proc/cpuinfo
#
# Vector Collector Connectivity:
# 1. Test TCP connection: telnet YOUR_VECTOR_IP 6514
# 2. Check network routes: ip route show
# 3. Monitor network errors: netstat -i
# 4. Verify firewall rules: iptables -L
#
# IBM Z Specific Checks:
# 1. Check z/VM guest status (if applicable): vmcp q userid
# 2. Monitor LPAR resources: vmstat -s
# 3. Check IBM Z I/O configuration: lscss
# 4. Verify channel paths: lschp

#####################################################################
# IBM Z PERFORMANCE TUNING RECOMMENDATIONS
#####################################################################

# Memory Optimization for IBM Z:
# - Start with conservative queue sizes
# - Monitor memory usage over time
# - Adjust based on LPAR memory allocation
# - Consider z/VM memory balancing
#
# CPU Optimization for IBM Z:
# - Use fewer worker threads (IBM Z processors are efficient)
# - Monitor CPU utilization patterns
# - Adjust based on LPAR CPU allocation
# - Consider IBM Z processor characteristics
#
# Network Optimization for IBM Z:
# - Prefer HiperSockets for intra-mainframe communication
# - Use OSA for external connectivity
# - Monitor network utilization
# - Optimize message sizes for network efficiency
#
# Reliability for IBM Z:
# - Enable queue persistence for critical messages
# - Monitor error logs regularly
# - Implement proper backup strategies
# - Consider IBM Z high availability features

#####################################################################
# END OF IBM Z VECTOR FORWARDER CONFIGURATION
#####################################################################
