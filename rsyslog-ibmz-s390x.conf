#####################################################################
# IBM Z (s390x) OPTIMIZED RSYSLOG CONFIGURATION - RHEL 8
# File: /etc/rsyslog.conf or /etc/rsyslog.d/10-ibmz-rsyslog.conf
#
# Purpose: Main rsyslog configuration optimized for IBM Z mainframe architecture
# Target: RHEL 8 on IBM Z (s390x) systems
# Architecture: IBM Z mainframe with HiperSockets networking
# Rsyslog Version: 8.2102.0+ (RHEL 8 default)
#
# IBM Z SPECIFIC OPTIMIZATIONS:
# 1. Memory-efficient queue management for mainframe environments
# 2. HiperSockets network optimization for intra-CPC communication
# 3. CPU-efficient processing for IBM Z processors
# 4. Optimized for shared memory and virtual machine environments
# 5. Enhanced reliability for mission-critical mainframe workloads
#
# Author: System Administrator
# Version: 1.0 (IBM Z s390x Optimized)
# Date: 2024-12-24
#####################################################################

#####################################################################
# IBM Z ARCHITECTURE GLOBAL SETTINGS
#####################################################################

# Working directory optimized for IBM Z storage hierarchy
$WorkDirectory /var/lib/rsyslog

# IBM Z Memory Management - Conservative settings for shared environments
# Reduced memory footprint for efficient VM resource utilization
$MainMsgQueueSize 8192
$MainMsgQueueHighWaterMark 6144
$MainMsgQueueLowWaterMark 2048
$MainMsgQueueDiscardMark 7680

# IBM Z CPU Optimization - Efficient processing threads
# Optimized for IBM Z processor characteristics
$MainMsgQueueWorkerThreads 2
$MainMsgQueueWorkerThreadMinimumMessages 1000

# Message size limits - Conservative for IBM Z network efficiency
$MaxMessageSize 32k

# IBM Z Network Optimization - HiperSockets friendly settings
$PreserveFQDN on
$EscapeControlCharactersOnReceive off

# IBM Z Reliability Settings - Enhanced for mainframe environments
$RepeatedMsgReduction on
$ActionFileEnableSync off
$ActionResumeRetryCount -1

# IBM Z Performance - Optimized timestamp handling
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

#####################################################################
# IBM Z MODULE LOADING (RHEL 8 Compatible)
#####################################################################

# Core modules - already loaded in RHEL 8 main config
# Avoiding duplicate loading for IBM Z stability
# $ModLoad imuxsock    # Unix socket input (usually pre-loaded)
# $ModLoad imjournal   # Journal input (usually pre-loaded)
# $ModLoad imklog      # Kernel logging (usually pre-loaded)

# Network modules for IBM Z HiperSockets optimization
# Only load if not already present in main configuration
# $ModLoad imudp       # UDP input (load only if needed)
# $ModLoad imtcp       # TCP input (load only if needed)

#####################################################################
# IBM Z MESSAGE TEMPLATES (Optimized for Mainframe)
#####################################################################

# IBM Z Standard Template - Optimized for HiperSockets transmission
$template IBMZStandardFormat,"<%PRI%>%TIMESTAMP% %HOSTNAME% %syslogtag%%msg%\n"

# IBM Z Enhanced Template - With mainframe-specific metadata
$template IBMZEnhancedFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [ibmz@32473 arch=\"s390x\" lpar=\"%HOSTNAME%\" facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\"] %MSG%\n"

# IBM Z Compact Template - Memory efficient for high-volume logging
$template IBMZCompactFormat,"<%PRI%>%TIMESTAMP:::date-unixtimestamp% %HOSTNAME% %syslogtag:1:32%%msg:1:256%\n"

#####################################################################
# IBM Z QUEUE CONFIGURATION (Memory Optimized)
#####################################################################

# IBM Z Critical Priority Queue - For system-critical messages
$ActionQueueFileName ibmz_critical_queue
$ActionQueueMaxDiskSpace 512m
$ActionQueueSaveOnShutdown on
$ActionQueueType LinkedList
$ActionResumeRetryCount -1
$ActionQueueSize 4096
$ActionQueueDiscardMark 3840
$ActionQueueHighWaterMark 3072
$ActionQueueLowWaterMark 1024
$ActionQueueWorkerThreads 1
$ActionQueueTimeoutEnqueue 100

# Forward critical system messages
*.emerg                                         @@************:6514;IBMZEnhancedFormat
*.alert                                         @@************:6514;IBMZEnhancedFormat
*.crit                                          @@************:6514;IBMZEnhancedFormat
authpriv.*                                      @@************:6514;IBMZEnhancedFormat

# Reset queue for standard priority messages
$ActionQueueFileName ibmz_standard_queue
$ActionQueueMaxDiskSpace 256m
$ActionQueueSize 2048
$ActionQueueDiscardMark 1920
$ActionQueueHighWaterMark 1536
$ActionQueueLowWaterMark 512
$ActionQueueWorkerThreads 1

# Forward standard messages with IBM Z optimizations
kern.*                                          @@************:6514;IBMZStandardFormat
daemon.*                                        @@************:6514;IBMZStandardFormat
*.err                                           @@************:6514;IBMZStandardFormat
*.warning                                       @@************:6514;IBMZStandardFormat
*.info                                          @@************:6514;IBMZStandardFormat


#####################################################################
# IBM Z LEGACY FORWARDING (QRadar Integration)
#####################################################################

# QRadar forwarding - using UDP for IBM Z network efficiency
local6.*                                        @9.214.44.206:514

#####################################################################
# IBM Z MESSAGE FILTERING (Performance Optimized)
#####################################################################

# IBM Z optimized message filtering - reduce network traffic
:msg, contains, "BESClient"                     stop
:msg, contains, "cinfo"                         stop
:msg, contains, "date"                          stop
:msg, contains, "java"                          stop
:msg, contains, "monitor_home_fo"               stop
:msg, contains, "grep"                          stop
:msg, contains, "md5sum"                        stop
:msg, contains, "watchlist4.db2"               stop
:msg, contains, "sudo_nrpe_sync"               stop
:msg, contains, "awk"                           stop
:msg, contains, "PROCTITLE"                     stop

# IBM Z specific filters - mainframe noise reduction
:msg, contains, "z/VM"                          /var/log/ibmz-hypervisor.log
:msg, contains, "HiperSockets"                  /var/log/ibmz-network.log
:msg, contains, "s390"                          /var/log/ibmz-arch.log

#####################################################################
# IBM Z ERROR HANDLING AND MONITORING
#####################################################################

# IBM Z enhanced error handling
$ErrorMessagesToStderr off
$LogRSyslogStatusMessages on

# IBM Z monitoring - log forwarding errors
$ActionExecOnlyOnceEveryInterval 600
& /var/log/ibmz-rsyslog-errors.log

#####################################################################
# IBM Z PERFORMANCE TUNING NOTES
#####################################################################

# IBM Z Architecture Considerations:
# 1. HiperSockets: Use for intra-CPC communication when possible
# 2. Memory: Conservative queue sizes due to shared memory model
# 3. CPU: Fewer worker threads due to efficient IBM Z processors
# 4. Network: Optimized for mainframe network characteristics
# 5. Reliability: Enhanced error handling for mission-critical systems
#
# HiperSockets Configuration:
# - Provides memory-speed communication between LPARs
# - Reduces CPU overhead compared to traditional networking
# - Ideal for log forwarding within the same mainframe
#
# Memory Optimization:
# - Smaller queue sizes to reduce memory footprint
# - Efficient for z/VM and LPAR environments
# - Optimized for IBM Z memory hierarchy
#
# CPU Efficiency:
# - Fewer worker threads leverage IBM Z processor efficiency
# - Reduced context switching overhead
# - Optimized for mainframe workload characteristics

#####################################################################
# IBM Z DEPLOYMENT INSTRUCTIONS
#####################################################################

# 1. BACKUP EXISTING CONFIGURATION:
#    sudo cp /etc/rsyslog.conf /etc/rsyslog.conf.backup.$(date +%Y%m%d)
#
# 2. DEPLOY IBM Z CONFIGURATION:
#    sudo cp rsyslog-ibmz-s390x.conf /etc/rsyslog.d/10-ibmz-rsyslog.conf
#    sudo chown root:root /etc/rsyslog.d/10-ibmz-rsyslog.conf
#    sudo chmod 644 /etc/rsyslog.d/10-ibmz-rsyslog.conf
#
# 3. UPDATE VECTOR COLLECTOR IP:
#    sudo sed -i 's/9\.214\.57\.164/YOUR_VECTOR_IP/g' /etc/rsyslog.d/10-ibmz-rsyslog.conf
#
# 4. VALIDATE CONFIGURATION:
#    sudo rsyslogd -N1 -f /etc/rsyslog.conf
#
# 5. TEST HIPERSOCKETS CONNECTIVITY (if applicable):
#    # Check HiperSockets interfaces
#    ip addr show | grep -i hsi
#    # Test connectivity to Vector collector
#    nc -zv YOUR_VECTOR_IP 6514
#
# 6. RESTART RSYSLOG SERVICE:
#    sudo systemctl restart rsyslog
#    sudo systemctl status rsyslog
#
# 7. TEST IBM Z LOGGING:
#    logger -p local0.info "IBM Z system test from $(hostname) - $(uname -m)"
#    logger -p authpriv.warning "IBM Z authentication test from s390x system"
#
# 8. MONITOR IBM Z SPECIFIC LOGS:
#    sudo tail -f /var/log/ibmz-system.log
#    sudo tail -f /var/log/ibmz-rsyslog-errors.log
#    sudo ls -la /var/lib/rsyslog/ibmz_*_queue*

#####################################################################
# IBM Z TROUBLESHOOTING GUIDE
#####################################################################

# HiperSockets Issues:
# 1. Check HiperSockets configuration: lsqeth
# 2. Verify HiperSockets interfaces: ip link show type hsi
# 3. Check HiperSockets statistics: cat /proc/qeth_perf_stats
#
# Memory Issues:
# 1. Monitor queue file sizes: du -sh /var/lib/rsyslog/ibmz_*
# 2. Check available memory: free -h
# 3. Adjust queue sizes if needed for your LPAR configuration
#
# CPU Performance:
# 1. Monitor rsyslog CPU usage: top -p $(pgrep rsyslogd)
# 2. Check IBM Z processor utilization: vmstat 1 5
# 3. Adjust worker thread count if needed
#
# Network Connectivity:
# 1. Test Vector collector connectivity: telnet YOUR_VECTOR_IP 6514
# 2. Check network statistics: ss -tuln | grep 6514
# 3. Monitor network errors: netstat -i
#
# IBM Z Specific Checks:
# 1. Verify architecture: uname -m (should show s390x)
# 2. Check z/VM guest status: vmcp q userid (if running under z/VM)
# 3. Monitor LPAR resources: cat /proc/sysinfo

#####################################################################
# END OF IBM Z OPTIMIZED RSYSLOG CONFIGURATION
#####################################################################
