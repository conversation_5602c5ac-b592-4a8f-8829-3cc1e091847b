# rsyslog configuration file - RHEL9 IBM Z (s390x) OPTIMIZED VERSION
# Target: RHEL 9 on IBM Z (s390x) architecture
# Architecture: IBM Z mainframe (s390x)
# Rsyslog Version: 8.2102.0+ (RHEL 9 default)
# 
# OPTIMIZATIONS FOR IBM Z (s390x):
# - Reduced memory footprint for mainframe efficiency
# - Optimized buffer sizes for s390x memory architecture
# - Enhanced queue management for high-throughput mainframe workloads
# - RHEL9 systemd-journald integration improvements
# - s390x-specific performance tuning parameters
#
# Author: System Administrator
# Version: 2.0 (RHEL9 s390x Optimized)
# Date: 2024-12-19
#####################################################################

#### MODULES ####
# RHEL 9 COMPATIBLE: Most modules are pre-loaded in main config
# Only load additional modules if specifically needed

# Enhanced journald integration for RHEL9
# $ModLoad imjournal # Usually already loaded in main config
# $ModLoad imuxsock # Usually already loaded in main config

# Network modules (commented out to avoid conflicts)
# Uncomment only if this server needs to receive remote logs
# $ModLoad imudp
# $UDPServerRun 514
# $ModLoad imtcp
# $InputTCPServerRun 514

#### GLOBAL DIRECTIVES - IBM Z (s390x) OPTIMIZED ####

# Working directory optimized for s390x storage
$WorkDirectory /var/lib/rsyslog

# IBM Z memory optimization - reduced buffer sizes
# s390x systems benefit from smaller, more frequent I/O operations
$MainMsgQueueSize 8192
$MainMsgQueueHighWaterMark 6144
$MainMsgQueueLowWaterMark 2048
$MainMsgQueueDiscardMark 7680

# s390x-optimized message size limits
# IBM Z systems handle smaller messages more efficiently
$MaxMessageSize 32k

# RHEL9 timestamp format with enhanced precision
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# IBM Z networking optimizations
$PreserveFQDN on
$EscapeControlCharactersOnReceive off

# RHEL9 systemd-journald integration
# Enhanced journal reading for better RHEL9 compatibility
$IMJournalStateFile imjournal.state
$IMJournalRatelimitInterval 600
$IMJournalRatelimitBurst 20000

# s390x I/O optimization
# Reduce sync operations for better mainframe performance
$ActionFileEnableSync off
$ActionResumeInterval 10
$ActionResumeRetryCount -1

#### IBM Z (s390x) PERFORMANCE TEMPLATES ####

# s390x optimized syslog format with reduced overhead
$template S390XFormat,"<%PRI%>%TIMESTAMP% %HOSTNAME% %syslogtag%%msg%\n"

# Enhanced format for critical systems monitoring
$template S390XDetailedFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [s390x@32473 facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" arch=\"s390x\" os=\"rhel9\"] %MSG%\n"

# Compact format for high-volume logging
$template S390XCompactFormat,"%TIMESTAMP:::date-rfc3339% %HOSTNAME% %syslogtag%%msg%\n"

#### RULES - IBM Z (s390x) OPTIMIZED ####

# Emergency and critical messages - highest priority
*.emerg                                                 :omusrmsg:*
*.alert                                                 /var/log/alert.log
*.crit                                                  /var/log/critical.log

# Authentication logs - security critical for mainframe
auth,authpriv.*                                         /var/log/secure

# System messages - filtered for s390x relevance
*.info;mail.none;authpriv.none;cron.none;local6.none   /var/log/messages

# Mail system logs
mail.*                                                  -/var/log/maillog

# Cron logs
cron.*                                                  /var/log/cron

# Boot messages - important for s390x IPL process
local7.*                                                /var/log/boot.log

# Kernel messages - critical for s390x hardware monitoring
kern.*                                                  /var/log/kernel.log

#### REMOTE FORWARDING - IBM Z (s390x) OPTIMIZED ####

# QRadar forwarding with s390x optimizations
# Reduced queue size for mainframe memory efficiency
$ActionQueueFileName s390x_qradar_queue
$ActionQueueMaxDiskSpace 256m
$ActionQueueSaveOnShutdown on
$ActionQueueType LinkedList
$ActionResumeRetryCount -1
$ActionQueueSize 4096
$ActionQueueDiscardMark 3840
$ActionQueueHighWaterMark 3072
$ActionQueueLowWaterMark 1024
$ActionQueueWorkerThreads 2

# Forward to QRadar with s390x identification
local6.* @************:514;S390XFormat

# Reset queue for Vector collector
$ActionQueueFileName s390x_vector_queue
$ActionQueueMaxDiskSpace 512m
$ActionQueueSize 6144
$ActionQueueDiscardMark 5760
$ActionQueueHighWaterMark 4608
$ActionQueueLowWaterMark 1536
$ActionQueueWorkerThreads 3

# Forward all logs to Vector collector with enhanced format
*.* @@9.214.57.164:6514;S390XDetailedFormat

# Reset queue settings for local logging
$ActionQueueFileName
$ActionQueueMaxDiskSpace
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount
$ActionQueueSize
$ActionQueueDiscardMark
$ActionQueueHighWaterMark
$ActionQueueLowWaterMark
$ActionQueueWorkerThreads

#### MESSAGE FILTERING - s390x OPTIMIZED ####
# Filter out noise common in mainframe environments

# IBM Z specific filters
:msg, contains, "s390" stop
:msg, contains, "zfcp" stop
:msg, contains, "dasd" stop
:msg, contains, "ccw" stop

# Standard application filters
:msg, contains, "BESClient" stop
:msg, contains, "cinfo" stop
:msg, contains, "date" stop
:msg, contains, "java" stop
:msg, contains, "monitor_home_fo" stop
:msg, contains, "grep" stop
:msg, contains, "md5sum" stop
:msg, contains, "watchlist4.db2" stop
:msg, contains, "sudo_nrpe_sync" stop
:msg, contains, "awk" stop
:msg, contains, "PROCTITLE" stop

#### RHEL9 s390x DEPLOYMENT NOTES ####
#
# 1. ARCHITECTURE CONSIDERATIONS:
#    - s390x uses different memory management than x86_64
#    - Smaller queue sizes optimize for mainframe memory efficiency
#    - Enhanced I/O patterns for IBM Z storage subsystems
#
# 2. RHEL9 IMPROVEMENTS:
#    - Better systemd-journald integration
#    - Enhanced timestamp precision
#    - Improved error handling and recovery
#
# 3. DEPLOYMENT COMMANDS:
#    sudo cp rhel9-s390x-rsyslog.conf /etc/rsyslog.d/10-s390x-config.conf
#    sudo chown root:root /etc/rsyslog.d/10-s390x-config.conf
#    sudo chmod 644 /etc/rsyslog.d/10-s390x-config.conf
#    sudo rsyslogd -N1 -f /etc/rsyslog.conf
#    sudo systemctl restart rsyslog
#
# 4. MONITORING:
#    sudo journalctl -u rsyslog -f
#    sudo ls -la /var/lib/rsyslog/s390x_*_queue*
#    sudo netstat -an | grep :514
#
# 5. s390x SPECIFIC TUNING:
#    - Monitor memory usage: cat /proc/meminfo
#    - Check I/O patterns: iostat -x 1
#    - Verify network performance: ss -tuln
#
#####################################################################
# END OF RHEL9 s390x CONFIGURATION
#####################################################################
