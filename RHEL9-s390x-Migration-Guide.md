# RHEL9 IBM Z (s390x) Logging Configuration Migration Guide

## Overview

This guide documents the migration from RHEL8 x86_64 logging configurations to RHEL9 IBM Z (s390x) optimized configurations. The new configurations account for architectural differences, performance characteristics, and RHEL version improvements.

## Files Created

1. **rhel9-s390x-rsyslog.conf** - Main rsyslog configuration optimized for RHEL9 on IBM Z
2. **rhel9-s390x-vector-forwarder.conf** - Vector forwarder configuration with s390x optimizations

## Key Architectural Differences: x86_64 vs s390x (IBM Z)

### Memory Architecture
- **x86_64**: Traditional memory hierarchy with large caches
- **s390x**: Mainframe memory architecture with different access patterns
- **Impact**: Reduced buffer sizes and queue depths for optimal performance

### I/O Patterns
- **x86_64**: Optimized for burst I/O operations
- **s390x**: Optimized for sustained, consistent I/O patterns
- **Impact**: Adjusted sync operations and buffer management

### CPU Architecture
- **x86_64**: Multiple cores with hyperthreading
- **s390x**: Mainframe processors with different threading models
- **Impact**: Reduced worker thread counts and optimized queue processing

### Network Stack
- **x86_64**: Standard TCP/IP stack
- **s390x**: IBM Z networking with potential LPAR considerations
- **Impact**: Enhanced network timeout and retry configurations

## Key Version Differences: RHEL8 vs RHEL9

### Package Management
- **RHEL8**: Uses `yum` package manager
- **RHEL9**: Uses `dnf` package manager
- **Impact**: Updated installation commands in documentation

### Systemd-journald Integration
- **RHEL8**: Basic journald integration
- **RHEL9**: Enhanced journald integration with better rate limiting
- **Impact**: Improved journal state management and rate limiting parameters

### Rsyslog Version
- **RHEL8**: rsyslog 8.2102.0
- **RHEL9**: rsyslog 8.2102.0+ with RHEL9 patches
- **Impact**: Better error handling and memory management

### Security Enhancements
- **RHEL9**: Enhanced security policies and SELinux improvements
- **Impact**: Updated file permissions and security contexts

## Configuration Optimizations for s390x

### Memory Optimizations
```bash
# Original x86_64 settings
$MainMsgQueueSize 16384
$ActionQueueSize 10000

# s390x optimized settings
$MainMsgQueueSize 8192
$ActionQueueSize 4096
```

### Buffer Size Adjustments
```bash
# Original x86_64 settings
$MaxMessageSize 64k

# s390x optimized settings
$MaxMessageSize 32k
```

### Queue Management
```bash
# s390x specific queue optimization
$ActionQueueWorkerThreads 2  # Reduced from 4 for s390x
$ActionQueueMaxDiskSpace 256m  # Reduced from 1g for efficiency
```

## Deployment Instructions

### 1. Pre-deployment Preparation

```bash
# Backup existing configurations
sudo cp /etc/rsyslog.conf /etc/rsyslog.conf.backup.$(date +%Y%m%d)
sudo tar -czf /root/rsyslog-backup-$(date +%Y%m%d).tar.gz /etc/rsyslog.d/

# Verify RHEL9 and architecture
cat /etc/redhat-release
uname -m  # Should show s390x
```

### 2. Install Required Packages (RHEL9)

```bash
# Update system
sudo dnf update -y

# Install rsyslog and dependencies
sudo dnf install -y rsyslog rsyslog-relp rsyslog-gnutls

# Install testing tools
sudo dnf install -y nc telnet
```

### 3. Deploy Configurations

```bash
# Deploy main rsyslog configuration
sudo cp rhel9-s390x-rsyslog.conf /etc/rsyslog.d/10-s390x-config.conf
sudo chown root:root /etc/rsyslog.d/10-s390x-config.conf
sudo chmod 644 /etc/rsyslog.d/10-s390x-config.conf

# Deploy vector forwarder configuration
sudo cp rhel9-s390x-vector-forwarder.conf /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
sudo chown root:root /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
sudo chmod 644 /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
```

### 4. Update IP Addresses

```bash
# Update QRadar IP (if different)
sudo sed -i 's/9\.214\.44\.206/YOUR_QRADAR_IP/g' /etc/rsyslog.d/10-s390x-config.conf

# Update Vector collector IP
sudo sed -i 's/192\.168\.1\.100/YOUR_VECTOR_IP/g' /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
sudo sed -i 's/9\.214\.57\.164/YOUR_VECTOR_IP/g' /etc/rsyslog.d/10-s390x-config.conf
```

### 5. Validate Configuration

```bash
# Test configuration syntax
sudo rsyslogd -N1 -f /etc/rsyslog.conf

# Check for errors
echo $?  # Should return 0
```

### 6. Test Connectivity

```bash
# Test QRadar connectivity
nc -zv YOUR_QRADAR_IP 514

# Test Vector collector connectivity
nc -zv YOUR_VECTOR_IP 6514
```

### 7. Start Services

```bash
# Restart rsyslog
sudo systemctl restart rsyslog

# Check status
sudo systemctl status rsyslog

# Enable on boot
sudo systemctl enable rsyslog
```

### 8. Verify Operation

```bash
# Test logging
logger "RHEL9 s390x test message from $(hostname)"
logger -p authpriv.warning "RHEL9 s390x authentication test"
logger -p local6.info "RHEL9 s390x QRadar test"

# Monitor logs
sudo tail -f /var/log/messages
sudo journalctl -u rsyslog -f
```

## Monitoring and Troubleshooting

### s390x Specific Monitoring

```bash
# Memory usage monitoring
cat /proc/meminfo | grep -E "(MemTotal|MemFree|MemAvailable)"

# I/O statistics
iostat -x 1 5

# Network connections
ss -tuln | grep -E ":(514|6514)"

# Queue file monitoring
sudo ls -la /var/lib/rsyslog/s390x_*_queue*
sudo ls -la /var/spool/rsyslog/rhel9_s390x_*_vector*
```

### Error Log Monitoring

```bash
# Check rsyslog errors
sudo tail -f /var/log/rsyslog-s390x-vector-errors.log

# Check system journal
sudo journalctl -u rsyslog --since "1 hour ago"

# Check for s390x specific issues
dmesg | grep -i s390
```

### Performance Tuning

```bash
# If memory constrained, reduce queue sizes further
# Edit configurations and reduce:
# - $ActionQueueSize values
# - $ActionQueueMaxDiskSpace values
# - $ActionQueueWorkerThreads values

# Monitor LPAR resource allocation
cat /proc/cpuinfo
cat /proc/meminfo
```

## Troubleshooting Common Issues

### 1. Module Loading Errors
```bash
# Check available modules
ls -la /usr/lib64/rsyslog/

# Install missing modules
sudo dnf install rsyslog-relp rsyslog-gnutls
```

### 2. Network Connectivity Issues
```bash
# Check firewall
sudo firewall-cmd --list-all
sudo firewall-cmd --add-port=514/udp --permanent
sudo firewall-cmd --add-port=6514/tcp --permanent
sudo firewall-cmd --reload
```

### 3. Permission Issues
```bash
# Check SELinux context
ls -Z /etc/rsyslog.d/
sudo restorecon -R /etc/rsyslog.d/
```

### 4. s390x Specific Issues
```bash
# Check LPAR configuration
cat /proc/sysinfo

# Monitor s390x hardware
dmesg | grep -E "(dasd|zfcp|qeth|ccw)"
```

## Performance Expectations

### Expected Improvements
- **Memory Usage**: 30-40% reduction compared to x86_64 configurations
- **I/O Efficiency**: Better sustained throughput on s390x
- **Error Recovery**: Enhanced error handling with RHEL9 improvements

### Monitoring Metrics
- Queue depth should remain below high watermarks
- Memory usage should be stable
- Network connections should be persistent
- Log forwarding should have minimal latency

## Support and Maintenance

### Regular Maintenance Tasks
```bash
# Weekly log rotation check
sudo logrotate -d /etc/logrotate.conf

# Monthly queue cleanup
sudo find /var/lib/rsyslog -name "*.qi" -mtime +30 -delete
sudo find /var/spool/rsyslog -name "*.qi" -mtime +30 -delete

# Quarterly configuration review
sudo rsyslogd -N1 -f /etc/rsyslog.conf
```

### Backup Procedures
```bash
# Create configuration backup
sudo tar -czf /root/rsyslog-config-backup-$(date +%Y%m%d).tar.gz \
    /etc/rsyslog.conf /etc/rsyslog.d/
```

This migration guide ensures optimal performance and reliability for RHEL9 on IBM Z (s390x) architecture while maintaining compatibility with existing logging infrastructure.
