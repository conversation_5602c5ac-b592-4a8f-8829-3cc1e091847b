#####################################################################
# RHEL 9 IBM Z (s390x) Vector Log Forwarder Configuration
# File: /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
#
# Purpose: Forward all RHEL 9 system logs to centralized Vector collector
# Target: RHEL 9 on IBM Z (s390x) mainframe systems
# Architecture: IBM Z mainframe (s390x)
# Vector Collector: TCP socket on port 6514
# Rsyslog Version: 8.2102.0+ (RHEL 9 default)
#
# IBM Z (s390x) OPTIMIZATIONS:
# - Memory-efficient queue sizing for mainframe environments
# - s390x-specific I/O patterns and buffer management
# - Enhanced error handling for mainframe reliability requirements
# - RHEL9 systemd-journald integration improvements
# - Optimized for IBM Z networking stack
#
# Author: System Administrator
# Version: 2.0 (RHEL9 s390x Optimized)
# Date: 2024-12-19
#####################################################################

#####################################################################
# CONFIGURATION VARIABLES (RHEL 9 s390x Compatible)
#####################################################################

# IMPORTANT: Replace ************* with your actual Vector collector IP address
# This configuration is optimized for IBM Z (s390x) architecture

#####################################################################
# MODULE LOADING (RHEL 9 s390x Compatible)
#####################################################################

# NOTE: Most modules are already loaded in /etc/rsyslog.conf on RHEL9
# Avoid duplicate loading to prevent errors
# RHEL9 has improved module management compared to RHEL8

# Enhanced module availability check for RHEL9
# If missing modules, install: dnf install rsyslog-relp rsyslog-gnutls
# RHEL9 uses dnf instead of yum

#####################################################################
# GLOBAL CONFIGURATION (RHEL 9 s390x Optimized)
#####################################################################

# Working directory optimized for s390x storage patterns
$WorkDirectory /var/spool/rsyslog

# Message size limits - optimized for IBM Z memory architecture
# s390x systems benefit from smaller message sizes
$MaxMessageSize 32k

# RHEL9 enhanced timestamp format
$ActionFileDefaultTemplate RSYSLOG_TraditionalFileFormat

# IBM Z networking optimizations
$PreserveFQDN on
$EscapeControlCharactersOnReceive off

# RHEL9 systemd-journald integration enhancements
$IMJournalStateFile imjournal.state
$IMJournalRatelimitInterval 300
$IMJournalRatelimitBurst 10000

#####################################################################
# RHEL 9 s390x MESSAGE TEMPLATES
#####################################################################

# s390x optimized template with architecture identification
$template RHEL9S390XVectorFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [rhel9s390x@32473 facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" arch=\"s390x\" os=\"rhel9\" source_host=\"%HOSTNAME%\"] %MSG%\n"

# Compact template for high-volume s390x environments
$template S390XCompactFormat,"<%PRI%>%TIMESTAMP:::date-rfc3339% %HOSTNAME% %syslogtag%%msg%\n"

# Enhanced template for mainframe monitoring
$template S390XMonitoringFormat,"<%PRI%>1 %TIMESTAMP:::date-rfc3339% %HOSTNAME% %APP-NAME% %PROCID% %MSGID% [s390x-monitor@32473 facility=\"%syslogfacility-text%\" severity=\"%syslogseverity-text%\" arch=\"s390x\" lpar=\"%HOSTNAME%\"] %MSG%\n"

#####################################################################
# PRIORITY-BASED FORWARDING (RHEL 9 s390x Optimized)
#####################################################################

# CRITICAL PRIORITY: s390x optimized high-priority queue
# Reduced sizes for mainframe memory efficiency
$ActionQueueFileName rhel9_s390x_critical_vector
$ActionQueueMaxDiskSpace 512m
$ActionQueueSaveOnShutdown on
$ActionQueueType LinkedList
$ActionResumeRetryCount -1
$ActionQueueSize 4096
$ActionQueueDiscardMark 3840
$ActionQueueHighWaterMark 3072
$ActionQueueLowWaterMark 1024
$ActionQueueWorkerThreads 2

# Forward critical messages with s390x identification
# IMPORTANT: Replace ************* with your Vector collector IP
*.emerg @@*************:6514;RHEL9S390XVectorFormat
*.alert @@*************:6514;RHEL9S390XVectorFormat
*.crit @@*************:6514;RHEL9S390XVectorFormat
authpriv.* @@*************:6514;RHEL9S390XVectorFormat

# Reset queue for medium priority - s390x optimized
$ActionQueueFileName rhel9_s390x_medium_vector
$ActionQueueMaxDiskSpace 256m
$ActionQueueSize 3072
$ActionQueueDiscardMark 2880
$ActionQueueHighWaterMark 2560
$ActionQueueLowWaterMark 768
$ActionQueueWorkerThreads 2

# Forward medium priority messages
kern.* @@*************:6514;RHEL9S390XVectorFormat
daemon.* @@*************:6514;RHEL9S390XVectorFormat
*.err @@*************:6514;RHEL9S390XVectorFormat
*.warning @@*************:6514;RHEL9S390XVectorFormat

# Reset queue for standard priority - s390x optimized
$ActionQueueFileName rhel9_s390x_standard_vector
$ActionQueueMaxDiskSpace 128m
$ActionQueueSize 2048
$ActionQueueDiscardMark 1920
$ActionQueueHighWaterMark 1792
$ActionQueueLowWaterMark 512
$ActionQueueWorkerThreads 1

# Forward all other messages with compact format for efficiency
*.info @@*************:6514;S390XCompactFormat

#####################################################################
# LOCAL LOGGING PRESERVATION (RHEL 9 s390x Standard)
#####################################################################

# Reset queue settings for local logging
$ActionQueueFileName
$ActionQueueMaxDiskSpace
$ActionQueueSaveOnShutdown off
$ActionQueueType Direct
$ActionResumeRetryCount
$ActionQueueSize
$ActionQueueDiscardMark
$ActionQueueHighWaterMark
$ActionQueueLowWaterMark
$ActionQueueWorkerThreads

# Standard RHEL 9 local logging (preserve existing functionality)
*.info;mail.none;authpriv.none;cron.none    /var/log/messages
authpriv.*                                  /var/log/secure
mail.*                                      /var/log/maillog
cron.*                                      /var/log/cron
*.emerg                                     :omusrmsg:*
uucp,news.crit                             /var/log/spooler
local7.*                                   /var/log/boot.log

# s390x specific logs
kern.*                                     /var/log/kernel.log

#####################################################################
# ERROR HANDLING AND MONITORING (RHEL 9 s390x)
#####################################################################

# Enhanced error logging for RHEL9
$ErrorMessagesToStderr off
$LogRSyslogStatusMessages on

# s390x specific monitoring with reduced interval for efficiency
$ActionExecOnlyOnceEveryInterval 600
& /var/log/rsyslog-s390x-vector-errors.log

#####################################################################
# s390x SPECIFIC FILTERS
#####################################################################

# Filter out s390x hardware noise
:msg, contains, "s390" stop
:msg, contains, "zfcp" stop
:msg, contains, "dasd" stop
:msg, contains, "ccw" stop
:msg, contains, "qeth" stop

#####################################################################
# RHEL 9 s390x DEPLOYMENT INSTRUCTIONS
#####################################################################

# 1. INSTALL REQUIRED PACKAGES (RHEL9):
#    sudo dnf install -y rsyslog
#    sudo dnf install -y nc telnet  # For testing connectivity
#
# 2. DEPLOY CONFIGURATION:
#    sudo cp rhel9-s390x-vector-forwarder.conf /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
#    sudo chown root:root /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
#    sudo chmod 644 /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
#
# 3. UPDATE VECTOR COLLECTOR IP:
#    sudo sed -i 's/192\.168\.1\.100/YOUR_VECTOR_IP/g' /etc/rsyslog.d/50-rhel9-s390x-vector-forwarder.conf
#
# 4. VALIDATE CONFIGURATION:
#    sudo rsyslogd -N1 -f /etc/rsyslog.conf
#
# 5. TEST CONNECTIVITY:
#    nc -zv YOUR_VECTOR_IP 6514
#
# 6. RESTART SERVICE:
#    sudo systemctl restart rsyslog
#    sudo systemctl status rsyslog
#
# 7. TEST LOGGING:
#    logger "RHEL9 s390x Vector forwarder test from $(hostname)"
#    logger -p authpriv.warning "RHEL9 s390x authentication test"
#
# 8. MONITOR (s390x specific):
#    sudo tail -f /var/log/rsyslog-s390x-vector-errors.log
#    sudo ls -la /var/spool/rsyslog/rhel9_s390x_*_vector*
#    sudo journalctl -u rsyslog -f
#
# 9. s390x PERFORMANCE MONITORING:
#    # Memory usage
#    cat /proc/meminfo | grep -E "(MemTotal|MemFree|MemAvailable)"
#    # I/O statistics
#    iostat -x 1 5
#    # Network connections
#    ss -tuln | grep :6514

#####################################################################
# s390x TROUBLESHOOTING NOTES
#####################################################################

# IBM Z (s390x) specific considerations:
# 1. Memory constraints: s390x systems may have different memory patterns
# 2. I/O patterns: Mainframe I/O is optimized for different patterns than x86
# 3. Network stack: IBM Z networking may have different characteristics
# 4. LPAR considerations: Multiple LPARs may affect resource allocation
#
# If performance issues occur on s390x:
# 1. Reduce queue sizes further if memory is constrained
# 2. Adjust worker thread counts based on available CPU resources
# 3. Monitor LPAR resource allocation
# 4. Check for s390x-specific kernel messages in dmesg
#
# RHEL9 vs RHEL8 differences:
# 1. Use dnf instead of yum for package management
# 2. Enhanced systemd-journald integration
# 3. Improved rsyslog error handling
# 4. Better memory management in RHEL9

#####################################################################
# END OF RHEL 9 s390x CONFIGURATION
#####################################################################
